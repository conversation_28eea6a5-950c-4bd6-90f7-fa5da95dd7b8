<?php

namespace App\Controller\Component;

use Cake\Controller\Component;

use Cake\Database\Expression\QueryExpression;
use Cake\ORM\TableRegistry;
use Cake\Mailer\Mailer;
use Cake\Core\Configure;
use Cake\Utility\Text;
use Cake\Event\Event;
use Cake\Utility\Security;
use Cake\Auth\DefaultPasswordHasher;
//use QRcode;
use Cake\Http\Session;
use Aws\Sns\SnsClient;
use Aws\Exception\AwsException;
use Twilio\Rest\Client;
use SendGrid\Mail\Mail;
use Cake\View\View;
use Cake\I18n\FrozenTime;
use Cake\Log\Log;
use Cake\Log\Engine\FileLog;

//require_once(ROOT .DS. "vendor" . DS  . 'qrcode' . DS . 'qrlib.php');
// Testing

class ZohoComponent extends Component
{
	protected $Orders;
    protected $Products;
    protected $Customers;    
    protected $ZohoSettings;
    protected $zohoSettings;

	public function initialize($config): void
    {
        parent::initialize($config);
        
        $this->Orders = TableRegistry::getTableLocator()->get('Orders');
        $this->Customers = TableRegistry::getTableLocator()->get('Customers');
        $this->ZohoSettings = TableRegistry::getTableLocator()->get('ZohoSettings');
        $this->Products = TableRegistry::getTableLocator()->get('Products');
        
        $zohoSettings = $this->ZohoSettings->getZohoCredentials();
        $this->zohoSettings = $zohoSettings;
    }

    // refresh access token
    public function refreshAccessToken ()
    {
        $client_id     = $this->zohoSettings['clientId'];
        $client_secret = $this->zohoSettings['client_secret'];
        $refresh_token = $this->zohoSettings['refresh_token'];

        $url = "https://accounts.zoho.in/oauth/v2/token";

        $postFields = http_build_query([
            'refresh_token' => $refresh_token,
            'client_id' => $client_id,
            'client_secret' => $client_secret,
            'grant_type' => 'refresh_token'
        ]);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, TRUE);        
        curl_setopt($ch, CURLOPT_POST, TRUE);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded'
        ]);
        $result = curl_exec($ch);
        curl_close($ch);
        return json_decode($result);
    }

    // checks and refreshes if expired
    public function getValidAccessToken()
    {
        $now = date('Y-m-d H:i:s');
        $access_token = $this->zohoSettings['access_token'] ?? null;
        $token_expiry = new FrozenTime($this->zohoSettings['token_expiry'] ?? null);
        //$now = FrozenTime::now();

        /*Sometimes the token may be seconds away from expiry when your call is made. Add a buffer of 30–60 seconds to avoid mid-call failure*/
        $bufferTime = FrozenTime::now()->addSeconds(60);

        if ($access_token && $token_expiry > $bufferTime) {
            return $access_token;
        }

        // Refresh token
        $response = $this->refreshAccessToken();

        if (!empty($response->access_token)) {

            $newToken = $response->access_token;
            $newExpiry = FrozenTime::now()->addSeconds($response->expires_in ?? 3600);

            // 1. Update in-memory object for this request
            $this->zohoSettings['access_token'] = $newToken;
            $this->zohoSettings['token_expiry'] = $newExpiry;
            
            // Update in DB
            $entity = $this->ZohoSettings->get($this->zohoSettings['id']);
            $entity->access_token = $newToken;
            $entity->token_expiry = $newExpiry;
            $this->ZohoSettings->save($entity);

            return $response->access_token;
        }

        throw new \Exception("Zoho access token refresh failed");
    }

    //get call
    public function getCall($url, $isDesk = false) {
        
        $access_token = $this->getValidAccessToken(); // checks and refreshes if expired

        $headers = [
            "Authorization: Zoho-oauthtoken {$access_token}",
            "Content-Type: application/json"
        ];
        if ($isDesk) {
            $org_id = $this->zohoSettings['orgId'] ?? null;
            if ($org_id) {
                $headers[] = "orgId: {$org_id}";
            }
        }

        $ch = curl_init($url);
        curl_setopt($ch,CURLOPT_HTTPHEADER,$headers);
        curl_setopt($ch,CURLOPT_RETURNTRANSFER,TRUE);
        curl_setopt($ch,CURLOPT_HTTPGET,TRUE);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        $info = curl_getinfo($ch);

        if($info['http_code']) {
            if($info['http_code'] == 200){
                curl_close($ch);
                return json_decode($response);
            }
            else {
                //$info['http_code'];
                curl_close($ch);
                return json_decode($response);
            }
        } else {
            return 'unauthorized';
        }
    }

    //post call
    public function postCall($url, $data, $isDesk = false) {

        $access_token = $this->getValidAccessToken(); // checks and refreshes if expired
       
        $headers = [
            "Authorization: Zoho-oauthtoken {$access_token}",
            "Content-Type: application/json"
        ];

        if ($isDesk) {
            $org_id = $this->zohoSettings['orgId'] ?? null;
            if ($org_id) {
                $headers[] = "orgId: {$org_id}";
            }
        }
        
        //$payload  = json_encode(array_filter(array("data"=>array($data))));
        $payload  = json_encode($data);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, TRUE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_POST, TRUE);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload );
        $response = curl_exec($ch);
        curl_close($ch);

        return json_decode($response);
    }

    //put call
    public function putCall($url, $data, $isDesk = false) {

        $access_token = $this->getValidAccessToken();

        $headers = [
            "Authorization: Zoho-oauthtoken {$access_token}",
            "Content-Type: application/json"
        ];

        if ($isDesk) {
            $org_id = $this->zohoSettings['orgId'] ?? null;
            if ($org_id) {
                $headers[] = "orgId: {$org_id}";
            }
        }

        $payload = json_encode($data);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        $response = curl_exec($ch);
        curl_close($ch);

        return json_decode($response);
    }


    //delete call
    public function deleteCall($url) {

        $access_token = $this->getValidAccessToken(); // checks and refreshes if expired
       
        $headers = [
            "Authorization: Zoho-oauthtoken {$access_token}",
            "Content-Type: application/json"
        ];

        $ch = curl_init();
        
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, TRUE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        $response = curl_exec($ch);
        curl_close($ch);

        return json_decode($response);
    }     

    // create custom field
    public function createCustomField($module, $fieldLabel, $dataType = "text", $length = 255)
    {
        $url = "https://www.zohoapis.in/crm/v8/settings/fields?module=$module";

        $payload = [
            "fields" => [
                [                    
                    "field_label" => $fieldLabel,
                    "data_type"   => $dataType
                ]
            ]
        ];

        if ($dataType == "text" && $length) {
            $payload['fields'][0]['length'] = $length;
        }
        
        $response = $this->postCall($url, $payload);

        // Handle response
        if (!empty($response->fields[0]->code) && $response->fields[0]->code == "SUCCESS") {
            return $response->fields[0]->details->id;
        } else {
            // You can also log response for debugging
            return false;
        }
    }

    //S delete custom field
    public function deleteCustomField($module, $fieldId)
    {
        $url = "https://www.zohoapis.in/crm/v8/settings/fields/$fieldId?module=$module";
                
        $response = $this->deleteCall($url);

        // Handle response
        if (!empty($response->data[0]->code) && $response->data[0]->code == "SUCCESS") {
            return $response->data[0]->details->id;
        } else {
            // You can also log response for debugging
            return false;
        }
    }

    //S list module field's 
    public function listModuleFields($module)
    {
        $url = "https://www.zohoapis.in/crm/v2/settings/fields?module=$module";

        $response = $this->getCall($url);

        echo "<pre>"; print_r($response); exit;
    }

    // create customer
    public function createCRMContactOLD($customer_id, $data)
    {
        $contact_data = [
            "First_Name"   => trim($data['first_name']),
            "Last_Name"    => trim($data['last_name']),            
            "Email"        => trim($data['email']),
            "Phone"        => trim($data['p_country_code']) . trim($data['phone_number']),
            "Mobile"       => trim($data['m_country_code']) . trim($data['mobile_no']),
            "Mailing_Street" => trim($data['full_address']),
            "Mailing_City"   => trim($data['city_name']),
            "Mailing_Zip"    => trim($data['zipcode']),
            "Description"    => "Customer"
        ];

        $url = "https://www.zohoapis.in/crm/v2/Contacts";

        $payload = [
            "data" => [$contact_data]
        ];

        $createContact = $this->postCall($url, $payload);

        if (!empty($createContact->data[0]->details->id)) {

            //update customer table
            $saveCustomer = $this->Customers->get($customer_id);
            $saveCustomer->zoho_contactId = $createContact->data[0]->details->id;
            $this->Customers->save($saveCustomer);

            return true;
        } else {            
            //Log::write('error', 'Failed to create Zoho CRM contact: ' . json_encode($createContact));
            return false;
        }
    }

    // create customer
    public function createCRMContact($customer_id, $signedup_from)
    {
        
        $customer = $this->Customers->customerById($customer_id);

        $user = $customer->user;
        $address = $customer->customer_addresses[0] ?? null;

        $full_address = '';
        $city_name = '';
        $zipcode = '';

        if ($address) {
            $full_address = $address->house_no . ', ' . $address->address_line1;
            if (!empty($address->address_line2)) {
                $full_address .= ', ' . $address->address_line2;
            }
            if (!empty($address->landmark)) {
                $full_address .= ', ' . $address->landmark;
            }

            $city_name = $address->city->city_name ?? '';
            $zipcode = $address->zipcode ?? '';
        }

        $lastName = trim((string) ($user->last_name ?? ''));
        if ($lastName === '') {
            $lastName = 'NA';
        }

        $contact_data = [
            "First_Name"     => trim($user->first_name ?? ''),
            "Last_Name"      => $lastName,
            "Email"          => trim($user->email ?? ''),
            "Phone"          => trim($customer->country_code ?? '') . trim($customer->phone_number ?? ''),
            "Mobile"         => trim($user->country_code ?? '') . trim($user->mobile_no ?? ''),
            "Mailing_Street" => trim($full_address),
            "Mailing_City"   => trim($city_name),
            "Mailing_Zip"    => trim($zipcode),
            "Description"    => "Customer",
            "Platform"       => $signedup_from
          //"Lead_Source"    => $signup_method
        ]; 

        // Add FCM token only if it exists
        if (!empty($customer->fcm_token)) {
            $contact_data["FCM_Token"] = $customer->fcm_token;
        }       

        $url = "https://www.zohoapis.in/crm/v2/Contacts";

        $payload = [
            "data" => [$contact_data]
        ];

        $createContact = $this->postCall($url, $payload);

        if (!empty($createContact->data[0]->details->id)) {

            //update customer table
            $customerEntity = $this->Customers->get($customer_id);
            $customerEntity->zoho_contactId = $createContact->data[0]->details->id;
            $this->Customers->save($customerEntity);

            return true;
        } else {            
            //Log::write('error', 'Failed to create Zoho CRM contact: ' . json_encode($createContact));
            return false;
        }
    }

    // create product
    public function createCRMProduct($product_id, $productData)
    {
        $productPayload = [
            //"Reference_Name"    => trim($productData['reference_name']),
            "Product_Name"      => trim($productData['name']),
            "Product_Code"      => trim($productData['sku']),
            //"Description"       => trim($productData['description']),
            "Product_Category"  => trim($productData['category_name']), 
            //"Manufacturer"      => trim($productData['manufacturer']),
            //"Unit_Price"        => (float) $productData['unit_price'],
            //"Qty_in_Stock"      => (float) $productData['qty_in_stock'],
            //"Qty_Ordered"       => (float) $productData['qty_ordered'],
            //"Qty_in_Demand"     => (float) $productData['qty_in_demand'],
            //"Reorder_Level"     => (float) $productData['reorder_level'],
            //"Commission_Rate"   => (float) $productData['commission_rate'],
            "Product_Active"    => (bool) $productData['status'],
            "Custom_Reference_ID" => $product_id
            //"Usage_Unit"        => trim($productData['usage_unit']),
            //"Taxable"           => (bool) $productData['taxable'],
            //"Tax"               => $productData['tax'], // Array of tax names
            //"Sales_Start_Date"  => $productData['sales_start_date'], // YYYY-MM-DD
            //"Sales_End_Date"    => $productData['sales_end_date'],
            //"Support_Start_Date"=> $productData['support_start_date'],
            //"Support_Expiry_Date"=> $productData['support_expiry_date'],
        ];

        // Optional fields for linking Owner, Handler, Vendor
        if (!empty($productData['owner_id'])) {
            $productPayload['Owner'] = ["id" => $productData['owner_id']];
        }
        if (!empty($productData['handler_id'])) {
            $productPayload['Handler'] = ["id" => $productData['handler_id']];
        }
        if (!empty($productData['vendor_id'])) {
            $productPayload['Vendor_Name'] = ["id" => $productData['vendor_id']];
        }

        $url = "https://www.zohoapis.in/crm/v2/Products";

        $payload = [
            "data" => [$productPayload]
        ];

        $createProduct = $this->postCall($url, $payload);

        if (!empty($createProduct->data[0]->details->id)) {

            //update product table
            $saveProduct = $this->Products->get($product_id);
            $saveProduct->zoho_productId = $createProduct->data[0]->details->id;
            $this->Products->save($saveProduct);

            return true;
        } else {
            //Log::write('error', 'Failed to create Zoho CRM product: ' . json_encode($createProduct));
            return false;
        }
    }

    // create products in batches
    public function batchInsertProductsToZoho($products)
    {
        if (empty($products)) {
            return ['success' => 0, 'failed' => 0];
        }

        $url = "https://www.zohoapis.in/crm/v2/Products";

        $payload = [
            "data" => []
        ];

        foreach ($products as $product) {
            $payload["data"][] = [
                "Product_Name"        => $product->name,
                "Product_Code"        => $product->sku,
              //"Description"         => $product->description,
                "Product_Category"    => $product->category_name ?? 'Default',                
                "Unit_Price"          => (float) $product->sales_price,
                "Product_Active"      => true,
                "Custom_Reference_ID" => $product->id // Your DB product ID
            ];
        }

        $response = $this->postCall($url, $payload);

        $successCount = 0;
        $failedCount = 0;
        $errors = [];

        if (isset($response->data) && is_array($response->data)) {
            
            foreach ($response->data as $index => $record) {
                if (isset($record->code) && $record->code == "SUCCESS") {
                    $zohoId = $record->details->id;
                    $dbId = $products[$index]->id;

                    //update product table
                    $saveProduct = $this->Products->get($dbId);
                    $saveProduct->zoho_productId = $zohoId;
                    $this->Products->save($saveProduct);
                    $successCount++;
                }  else {
                    $failedCount++;
                    $errors[] = $record;
                }
            }
        } else {
            $failedCount = count($products);
            $errors[] = 'Invalid or unexpected API response.';
        }

        return [
            'success' => $successCount,
            'failed'  => $failedCount,
            'errors'  => $errors
        ];    
    }

    // Create Deal
    public function createCRMDeal($order_id, $data)
    {
        $deal_data = [
            "Deal_Name"    => trim($data['order_number']), // Example: Order number or deal name
            "Stage"        => trim($data['stage']), // e.g., Qualification, Proposal, Closed Won
            "Amount"       => trim($data['amount']), // Total deal/order amount
            "Closing_Date" => trim($data['closing_date']), // YYYY-MM-DD format
            "Contact_Name" => [
                "id" => trim($data['zoho_contactId']) // Zoho Contact ID (already created)
            ],
            "Description"  => "Order placed via ".$data['order_online_source']
        ];

        $url = "https://www.zohoapis.in/crm/v2/Deals";

        $payload = [
            "data" => [$deal_data]
        ];

        $createDeal = $this->postCall($url, $payload);

        if (!empty($createDeal->data[0]->details->id)) {            

            //update order table
            $saveOrder = $this->Orders->get($order_id);
            $saveOrder->zoho_dealId = $createDeal->data[0]->details->id;
            $this->Orders->save($saveOrder);

            return true;
        } else {
            //Log::write('error', 'Failed to create Zoho CRM deal: ' . json_encode($createDeal));
            return false;
        }
    }

    // Create Sales Order
    public function createCRMSalesOrderOLD($order_id, $data)
    {
        
        $product_details = [];

        foreach ($data['order_items'] as $item) {
            $zoho_product_id = $this->Products->getZohoProductId($item['product_id']);

            $product_details[] = [
                "product" => ["id" => $zoho_product_id],
                "quantity" => $item['quantity'],
                "Unit Price" => $item['price'],
                "Discount" => 0
            ];
        }

        $sales_order_data = [
            "Subject"   => trim($data['order_number']),  // Sales Order title
            "Contact_Name" => [
                "id" => trim($data['zoho_contactId']) // Optional: Contact linked to this Sales Order
            ],
            "Discount"  => $data['offer_amount'],
            "Status"    => $data['status'],
            "Due_Date"  => trim($data['due_date']), // Optional: YYYY-MM-DD
            "Description"  => "Sales Order placed via ".$data['order_online_source'],
            "Shipping_City" => $data['shipping_city'],
            "Shipping_Street" => $data['shipping_address'],
            "Product_Details" => $product_details
        ];

        $url = "https://www.zohoapis.in/crm/v2/Sales_Orders";

        $payload = [
            "data" => [$sales_order_data]
        ];

        $createSalesOrder = $this->postCall($url, $payload);

        if (!empty($createSalesOrder->data[0]->details->id)) {

            //update order table
            $saveOrder = $this->Orders->get($order_id);
            $saveOrder->zoho_salesorderId = $createSalesOrder->data[0]->details->id;
            $this->Orders->save($saveOrder);

            return true;
        } else {
            //Log::write('error', 'Failed to create Zoho CRM Sales Order: ' . json_encode($createSalesOrder));
            return false;
        }
    }

    //S
    public function createCRMSalesOrder($order_id, $ordered_from)
    {
        // Fetch full order details
        $order = $this->Orders->orderDetail($order_id);

        if (!$order) {
            return false;
        }

        $product_details = [];

        foreach ($order->order_items as $item) {
            // Fetch Zoho product ID (assumes it's stored in Products table)
            $zoho_product_id = $item->product->zoho_productId ?? null;

            if (!$zoho_product_id) {
                // Optional: log missing Zoho product ID
                continue;
            }

            $product_details[] = [
                "product" => ["id" => $zoho_product_id],
                "quantity" => $item->quantity,
                "list_price" => (float) $item->price,
                "Discount" => 0
            ];
        }

        // Build full address
        $address = $order->customer_address;
        $full_address = $address->house_no . ', ' . $address->address_line1;
        if (!empty($address->address_line2)) {
            $full_address .= ', ' . $address->address_line2;
        }
        if (!empty($address->landmark)) {
            $full_address .= ', ' . $address->landmark;
        }

        $city_name = $address->city->city_name ?? '';

        $delivery_mode = $order->delivery_mode;
        $showroom_name = $order->showroom->name;

        // Set due date
        //$due_date = $order->delivery_date ?? $order->order_date;
        //$due_date = date('Y-m-d', strtotime($due_date));

        /*if (empty($order->customer->zoho_contactId)) {
            // create contact
            $customer_id = $order->customer_id;
            $signedup_from = $ordered_from;
            $zoho_res = $this->createCRMContact($customer_id, $signedup_from);
        } else {
            $zoho_contact_id = trim($order->customer->zoho_contactId);
        }*/

        // Prepare Zoho Sales Order payload
        $sales_order_data = [
            "Subject"         => trim($order->order_number),
            "Contact_Name"    => ["id" => isset($order->customer->zoho_contactId) ? trim($order->customer->zoho_contactId) : null],
            "Discount"        => $order->offer_amount,
            "Status"          => $order->status,
          //"Due_Date"        => $due_date,
          //"Description"     => "Sales Order placed via " . $order->order_online_source,
            "Description"     => "Sales Order placed via " . $ordered_from,
            "Shipping_City"   => $city_name,
            "Shipping_Street" => $full_address,
            "Platform"        => $ordered_from,
            "Delivery_Mode"   => $delivery_mode,
            "Showroom_Name"   => $showroom_name,
            "Product_Details" => $product_details
        ];

        // Send to Zoho CRM
        $url = "https://www.zohoapis.in/crm/v2/Sales_Orders";
        $payload = ["data" => [$sales_order_data]];
        $createSalesOrder = $this->postCall($url, $payload);

        // Save Zoho ID if success
        if (!empty($createSalesOrder->data[0]->details->id)) {
            $orderEntity = $this->Orders->get($order_id);
            $orderEntity->zoho_salesorderId = $createSalesOrder->data[0]->details->id;
            $orderEntity->zoho_last_synced_status = $order->status;
            $this->Orders->save($orderEntity);
            return true;
        }

        return false;
    }

    public function updateCRMSalesOrder($order_id)
    {
        // Fetch full order details
        $order = $this->Orders->orderDetail($order_id);

        if (!$order) {
            return false;
        }

        $product_details = [];

        foreach ($order->order_items as $item) {
            // Fetch Zoho product ID (assumes it's stored in Products table)
            $zoho_product_id = $item->product->zoho_productId ?? null;

            if (!$zoho_product_id) {
                // Optional: log missing Zoho product ID
                continue;
            }

            $product_details[] = [
                "product" => ["id" => $zoho_product_id],
                "quantity" => $item->quantity,
                "list_price" => (float) $item->price,
                "Discount" => 0
            ];
        }

        // Build full address
        $address = $order->customer_address;
        $full_address = $address->house_no . ', ' . $address->address_line1;
        if (!empty($address->address_line2)) {
            $full_address .= ', ' . $address->address_line2;
        }
        if (!empty($address->landmark)) {
            $full_address .= ', ' . $address->landmark;
        }

        $city_name = $address->city->city_name ?? '';

        $delivery_mode = $order->delivery_mode;
        $showroom_name = $order->showroom->name;

        // Set due date
        //$due_date = $order->delivery_date ?? $order->order_date;
        //$due_date = date('Y-m-d', strtotime($due_date));

        /*if (empty($order->customer->zoho_contactId)) {
            // create contact
            $customer_id = $order->customer_id;
            $signedup_from = $ordered_from;
            $zoho_res = $this->createCRMContact($customer_id, $signedup_from);
        } else {
            $zoho_contact_id = trim($order->customer->zoho_contactId);
        }*/

        // Prepare Zoho Sales Order payload
        $sales_order_data = [
            "Subject"         => trim($order->order_number),
            "Contact_Name"    => ["id" => isset($order->customer->zoho_contactId) ? trim($order->customer->zoho_contactId) : null],
            "Discount"        => $order->offer_amount,
            "Status"          => $order->status,
          //"Due_Date"        => $due_date,
          //"Description"     => "Sales Order placed via " . $order->order_online_source,
          //"Description"     => "Sales Order placed via " . $ordered_from,
            "Shipping_City"   => $city_name,
            "Shipping_Street" => $full_address,
          //"Platform"        => $ordered_from,
            "Delivery_Mode"   => $delivery_mode,
            "Showroom_Name"   => $showroom_name,
            "Product_Details" => $product_details
        ];

        // Send to Zoho CRM
        $url = "https://www.zohoapis.in/crm/v2/Sales_Orders/{$order->zoho_salesorderId}";
        $payload = ["data" => [$sales_order_data]];
        $createSalesOrder = $this->putCall($url, $payload);

        // Save Zoho ID if success
        /*if (!empty($createSalesOrder->data[0]->details->id)) {
            $orderEntity = $this->Orders->get($order_id);
            $orderEntity->zoho_salesorderId = $createSalesOrder->data[0]->details->id;
            $orderEntity->zoho_last_synced_status = $order->status;
            $this->Orders->save($orderEntity);
            return true;
        }*/

        return true;
    }

    public function updateOrderStatusToZoho($order)
    {
        try {

            $url = "https://www.zohoapis.in/crm/v2/Sales_Orders/{$order->zoho_salesorderId}";
            $data = [
                "data" => [
                    [
                        "Status" => $order->status
                    ]
                ]
            ];

            $response = $this->putCall($url, $data);

            if (isset($response->data[0]->code) && $response->data[0]->code === 'SUCCESS') {
                return ['success' => true];
            }

            //Log::write('debug', "Zoho SalesOrder update failed for order #{$order->id}: " . json_encode($response));
            return ['success' => false];

        } catch (\Exception $e) {
            //Log::write('debug', "Exception updating Zoho SalesOrder for order #{$order->id}: " . $e->getMessage());
            return ['success' => false];
        }
    }


}

?>