<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Zone> $zones
 */
?>

<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<style>

    .select2-container--default .select2-selection--single.is-invalid {
        border-color: #dc3545 !important;
        border-width: 1px;
        background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" fill="%23dc3545" viewBox="0 0 16 16"%3E%3Cpath d="M8 1a7 7 0 1 1 0 14A7 7 0 0 1 8 1zm0 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10zm.93-4.588-.857.857a.5.5 0 0 1-.708-.707l.857-.857V5.5a.5.5 0 0 1 1 0v2.912zM8 10.5a.75.75 0 1 1 0 1.5.75.75 0 0 1 0-1.5z"/%3E%3C/svg%3E');
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: 1em 1em;
        padding-right: 2.25rem;
    }

    .error-msg {
        color: #dc3545;
        font-size: 0.875em;
        margin-top: 4px;
        display: block;
    }

    input,
    select,
    textarea {
        width: 300px;
        padding: 5px;
        margin-bottom: 10px;
    }

    #toRemovePadding {
        margin-bottom: 0px !important;
        padding-bottom: 0px !important;
    }

    #remove-border {
        border: none;
        background-color: transparent;
    }

    #thead {
        background-color: #0d839b !important;
        color: white;
    }

</style>
<?php $this->end(); 
?>
<section class="section">
    <div
        class="section-header d-flex justify-content-between align-items-center mb-3"
    >
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
            </li>
            <li class="breadcrumb-item"><a
                    href="<?= $this->Url->build(['controller' => 'BL', 'action' => 'index']) ?>"><?= __('BLs') ?></a>
            </li>
            <li class="breadcrumb-item active"><?= __('Add Incoming Stock') ?></li>
        </ul>
        <button onclick="history.back();" class="d-flex align-items-center" id="back-button-mo">
            <small class="p-10 fw-bold"><?= __('BACK') ?></small>
            <span class="rotate me-2">⤣</span>
        </button>
    </div>
</section>    

<div class="section-body">
    <div class="container-fluid">
        <div class="card" id="toRemovePadding">
            <section id="view-product-product-details">
                <form id="add_stock_incoming_form" action="<?= $this->Url->build([
                            'controller' => 'WarehouseStockIncoming',
                            'action' => 'add'
                        ]); ?>" method="post" enctype="multipart/form-data">
                <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>
                <div class="row align-items-center mt-4">
                    <h3 class="font-22 mb-4">Add Incoming Stock</h3>
                    <div class="col-sm-6">
                        <!-- Supplier Dropdown -->
                        <label for="supplierSelect" class="fw-bold"><?= __('Supplier:') ?></label>
                        <input type="text" name="supplier_id"  class="form-control" value=<?php echo $supplier->name; ?> disabled>
                        <input type="hidden" name="supplier_id" id="supplierSelect" value="<?= h($supplier->id) ?>">

                            
                        <span id="supplier_error" style="color: #dc3545;display: none;"><?= __('Select Supplier') ?></span>

                        <input type="hidden" name="stock_request_id" id="stock_request_id" value="<?= h($Bl->stock_request_id) ?>">                        
                        <input type="hidden" name="bl_no" id="bl_no" value="<?= h($Bl->id) ?>">                        
                        <!-- BL Number Dropdown -->
                        <!-- <label for="billNoSelect" class="mt-2 fw-bold"><?= __('Select Bill Number:') ?></label> -->
                        <!-- <select name="stock_request_id" id="stock_request_id" class="form-control form-select select2">
                            <option value=""><?= __('-- No bill numbers available --') ?></option>
                        </select> -->
                        <!-- <span id="bill_no_error" style="color: #dc3545;display: none;"><?= __('Select Bill Number') ?></span>  -->
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="table-responsive" id="table_report" tabindex="1">
                            <!-- <table class="table dataTable table-hover table-xl mb-0 mt-4" id="stockTable" style="display:none;">
                                <thead>
                                    <tr>
                                        <th>< ?= __('Id') ?></th>
                                        <th>< ?= __('Product Name') ?></th>
                                        <th>< ?= __('Product Variant') ?></th>
                                        <th>< ?= __('Product Attribute') ?>e</th>
                                        <th>< ?= __('SKU') ?></th>
                                        <th>< ?= __('Quantity') ?></th>
                                    </tr>
                                </thead>
                                <tbody id="table_datalist">
                                </tbody>
                            </table> -->
                            <input type="hidden" min="1" class="form-control stock_movement_id" id="stock_movement_id" name="stock_movement_id">

                           
                            <table id="add_product_table" class="add_product_table table-responsive table table-striped mb-0 mt-4">
                                <thead>
                                  <tr class="back-clr" id="thead">
                                    <td class="fw-bold"><?= __('Product') ?></td>
                                    <td class="fw-bold"><?= __('Product Variant') ?></td>
                                    <td class="fw-bold"><?= __('Product Attribute') ?></td>
                                    <td class="fw-bold"><?= __('SKU') ?></td>
                                    <td class="fw-bold"><?= __('Remaining Quantity') ?></td>
                                    <td class="fw-bold"><?= __('Quantity') ?></td>
                                    <td class="fw-bold"><?= __('Defective Quantity') ?></td>
                                    <td class="fw-bold"><?= __('Damaged Quantity') ?></td>
                                    <td class="fw-bold"><?= __('Action') ?></td>
                                    
                                  </tr>
                                </thead>
                                <tbody>
                                  
                                 <?php 
                                 $i=0;
                                //  echo "<pre> index.php";
                                //  print_r($BLItems);
                                //  exit;
                                 foreach($BLItems as $item):
                                    // foreach($item as $key => $val):
                                    
                                        // echo $key;
                                        // echo "<pre>"; print_r($val); echo "</pre>"; 
                                    
                                      
                                    $product = $this->WebsiteFunction->getProductById($item->product_id);
                                   

                                    if(!is_null($item->product_variant_id) && $item->product_variant_id != 0):
                                        $variant = $this->WebsiteFunction->getVariantById($item->product_variant_id);
                                    else:
                                        $variant=null;
                                    endif;

                                   if(!is_null($item->product_attribute_id) && $item->product_attribute_id != 0):
                                        $attribute = $this->WebsiteFunction->getAttributeById($item->product_attribute_id);
                                        
                                        $attributeValue = $this->WebsiteFunction->getValueByAttributeValueId($attribute->attribute_value_id); 
                                                
                                    else:
                                        $attributeValue = '';    
                                    endif;
                                    ?>
                                  <tr>
                                    
                                    <!-- Product Dropdown -->
                                    <td class="col-sm-3">
                                    <input type="text" min="1" class="form-control product_id" id="product_name" value="<?php echo $product->name; ?>" disabled>

                                    <input type="hidden" min="1" class="form-control product_id" id="product_id" value="<?php echo $product->id; ?>" name="product_id[]">

                                     <input type="hidden" min="1" class="form-control stock_movement_item_id" id="stock_movement_item_id" name="stock_movement_item_id">
                                    </td>
                              
                                    <td>
                                    <input type="hidden" min="1" class="form-control variant_id" id="variant_id" value="<?= !empty($variant) ? $variant->id : '' ?>" name="product_variant_id[]">
                                    <input type="text" min="1" class="form-control variant_id" id="variant_name" value="<?= !empty($variant) ? $variant->variant_name : '' ?>" disabled >

                                  </td>

                                  <td>
                                    <input type="hidden" min="1" class="form-control attribute_id" id="attribute_id" value="<?= !empty($attribute) ? $attribute->id : '' ?>" name="product_attribute_id[]">
                                    <input type="text" min="1" class="form-control attribute_id" id="attribute_name" value="<?php echo isset($attributeValue[0]) ? $attributeValue[0] : '';  ?>" disabled >

                                    

                                  </td>

                                  <td>
                                        <span id="product_sku">
                                            <?php echo $product->sku; ?> 
                                        </span>
                                  </td>
                              
                                  <td>
                                        <?php
                                        if(isset($item->requested_quantity)):
                                            ?>
                                            <input type="number" min="1" class="form-control remaining_quantity" id="remaining_quantity" value = "<?php echo $item->bl_remaining_quantity; ?>" name="remaining_quantity[]" readonly>
                                            <span id="product_quantity_error" style="color: #dc3545;"></span>
                                        <?php elseif(isset($item->quantity)):

                                            ?>
                                            <input type="number" min="1" class="form-control remaining_quantity" id="remaining_quantity" value = "<?php echo $item->remaining_quantity; ?>" name="remaining_quantity[]">
                                            <span id="product_quantity_error" style="color: #dc3545;"></span>
                                        <?php endif ?>
                                    </td>
                                    <!-- Quantity Input -->
                                    <?php if(isset($item->requested_quantity)): ?>
                                         <td>
                                            <input type="number" min="1" class="form-control product_quantity" id="product_quantity" value = "<?php echo $item->bl_remaining_quantity; ?>" name="product_quantity[]">
                                            <span id="product_quantity_error" style="color: #dc3545;"></span>
                                        </td>
                                    <?php endif; 
                                        if(isset($item->quantity)): ?>
                                        <td>
                                            <input type="number" min="1" class="form-control product_quantity" id="product_quantity" value = "<?php echo $item->quantity; ?>" name="product_quantity[]">
                                            <span id="product_quantity_error" style="color: #dc3545;"></span>
                                        </td>
                                    <?php endif ?>

                                    
                                    <td>
                                      <input type="number" min="1" class="form-control defective_quantity" id="defective_quantity" name="defective_quantity[]" value="<?php echo $item->defective_quantity; ?>">
                                      <span id="defective_quantity_error" style="color: #dc3545;"></span>
                                    </td>
                                    <td>
                                      <input type="number" min="1" class="form-control damaged_quantity"    id="damaged_quantity" name="damaged_quantity[]" value="<?php echo $item->damaged_quantity; ?>">
                                      <span id="damaged_quantity_error" style="color: #dc3545;"></span>
                                    </td>
                                    <!-- Action Button -->
                                    <td>
                                    <input type="hidden" name="bl_item_id" id="bl_item_id" value="<?php echo $item->id; ?>">
                                    <?php if(isset($item->requested_quantity)): ?>
                                      <button type="button" id="updateQuest" class="btn btn-md updateQuest"><?= __('Save') ?></button>
                                      <a id="showItems" class="btn btn-md showItems" style="display: none;"><?= __('Items') ?></a>
                                      <input type="hidden" name="first_product_count" id="first_product_count" value=0 >
                                      <?php endif; ?>

                                    <?php
                                    
                                    if(isset($item->quantity)): ?>
            
                                      <a href="<?= $this->Url->build(
    ['controller' => 'WarehouseStockIncoming', 'action' => 'itemList', $qrLists[$i]->stock_movement_item_id, $qrLists[$i]->start_from, $qrLists[$i]->quantity]
) ?>" 
id="showItems" class="btn btn-md showItems">
    <?= __('Items') ?>
</a>
                                      <button type="button" id="updateQuest" class="btn btn-md updateQuest" style="display:none;"><?= __('Save') ?></button>
                                      
                                      <?php endif; ?>

                                    </td>
                                  </tr>
                                  <?php
                                //   endforeach;
                                endforeach; ?>
                                  
                                    </tbody>
                              </table>

                        </div>
                    </div>
                </div>
                <div class="row pt-4">
                    <div class="col-12">
                            
                            
                            
                              
                    </div>
                </div>
                <!-- <div class="row">
                    <div class="col-12 my-4">
                        <div class="text-left">
                            <button class="btn" id="save" type="button" style="border-radius: 5px;padding: 5px 25px;text-transform: uppercase;">
                                <?= __('Save') ?>
                            </button>
                        </div>
                    </div>
                </div> -->
                </form>
            </section>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script>

    let allFiles = [];

    

    // Finalize file processing
    function finalizeFileProcessing(validFiles, invalidFiles) {
        let html = "<ul>";
        invalidFiles.forEach((file) => {
            html += `<li>${file.file} - ${file.reason}</li>`; 
        });
        html += '</ul>';

        const wrapper = document.createElement('div');
        wrapper.innerHTML = html;

        if (invalidFiles.length > 0) {
            swal({
                title: "<?= __("Error") ?>", 
                content: wrapper,
                icon: "error",
                confirmButtonText: "<?= __("OK") ?>", 
                allowOutsideClick: true 
            });
        }

        if (validFiles.length > 0) {
            allFiles = validFiles.slice(0, 1);  // Keep only one valid file
            updateFileInput();
            renderPreviews();
        }
    }

    // Function to update the file input with only the single valid file
    function updateFileInput() {
        let dataTransfer = new DataTransfer();
        if (allFiles.length > 0) {
            dataTransfer.items.add(allFiles[0]);  // Add only the single valid file
        }
        document.getElementById('imageInput').files = dataTransfer.files;
    }

    // Render preview for the single image or document
    function renderPreviews() {
        let previewContainer = document.getElementById('imagePreviewContainer');
        previewContainer.innerHTML = '';

        if (allFiles.length > 0) {
            let file = allFiles[0];
            let li = document.createElement('li');
            li.classList.add('image-thumbnail');

            let fileName = file.name;
            let extension = fileName.slice((fileName.lastIndexOf(".") - 1 >>> 0) + 2);
            let nameWithoutExtension = fileName.slice(0, fileName.length - extension.length - 1);
            let shortName = nameWithoutExtension.length > 14 ? nameWithoutExtension.slice(0, 11) + '...' : nameWithoutExtension;
            shortName += '.' + extension;

            if (['pdf', 'doc', 'docx'].includes(extension)) {
                // For PDF, DOC, DOCX: Display an icon or filename
                li.innerHTML = `
                    <i class="fas fa-file-${extension === 'pdf' ? 'pdf' : 'word'}"></i>
                    <span class="file-name" title="${fileName}">${shortName}</span>
                    <button type="button" class="delete-img-btn">
                        <i class="fas fa-times"></i>
                    </button>
                `;
            } else {
                // For Images: Display image preview
                let reader = new FileReader();
                reader.onload = function (e) {
                    li.innerHTML = `
                        <img src="${e.target.result}" alt="Image Preview" class="preview-img"/>
                        <span class="image-name" title="${fileName}">${shortName}</span>
                        <button type="button" class="delete-img-btn">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                };
                reader.readAsDataURL(file);
            }

            previewContainer.appendChild(li);
        }
    }

   


    // Initialize Select2 for the dropdowns
    $(document).ready(function() {

        const supplierSelect = $('#supplierSelect');
        const billNoSelect = $('#billNoSelect');

        // Data from PHP passed to JavaScript
        const supplierData = <?= json_encode(value: $suppliers) ?>;

        // Event listener for supplier selection change
        // supplierSelect.on('change', function() {
            const selectedSupplierId = $('#supplierSelect').val();
            // Clear previous options in billNoSelect
            billNoSelect.empty().append(new Option('<?= __('-- No bill numbers available --') ?>', ''));

            // Check if a supplier is selected
            if (selectedSupplierId) {
                // If no supplier selected, clear products
                if (selectedSupplierId === '<?= __("Select Supplier") ?>') {
                    $('#product_option').html('<option value="0"><?= __('Select Product') ?></option>');
                    $('#product_option1').html('<option value="0"><?= __('Select Product') ?></option>');

                    return;
                }

                // Make AJAX call to fetch products for the selected supplier
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'WarehouseStockIncoming', 'action' => 'getProductsBySupplier']) ?>/' + selectedSupplierId,
                    method: 'GET',
                    headers: {
                            'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                        },
                    success: function (response) {

                        // Clear the current options in the product dropdown
                        $('#product_option').empty();
                        // Add a default "Select Product" option
                        $('#product_option').append('<option value="0">Select Product</option>');

                        $('#product_option1').empty();
                        // Add a default "Select Product" option
                        $('#product_option1').append('<option value="0">Select Product</option>');

                        // Loop through the returned products and populate the dropdown
                        response.products.forEach(function(product) {
                            var product_id = $('#product_id').val();
                            if(product_id == product.id)
                            {
                                $('#product_option').append(
                                    '<option value="' + product.id + '" data-product-sku="' + product.sku + '" selected>' +
                                        product.name +
                                    '</option>'
                                );
                            }
                            else{
                                $('#product_option').append(
                                    '<option value="' + product.id + '" data-product-sku="' + product.sku + '">' +
                                        product.name +
                                    '</option>'
                                );
                            }


                            $('#product_option1').append(
                                '<option value="' + product.id + '" data-product-sku="' + product.sku + '">' +
                                    product.name +
                                '</option>'
                            );
                        });

                        // Reinitialize the select2 dropdown
                        $('#product_option').select2();
                        $('#product_option1').select2();


                    },
                    error: function () {
                        
                        swal('<?= __('Failed') ?>', '<?= __('Error fetching products!') ?>', 'error');
                    }
                });

                // $('#stockTable tbody').empty();
                // $('#add_product_table tbody tr:not(:first-child)').remove();

                // Find the selected supplier data
                const selectedSupplier = supplierData.find(supplier => supplier.id === parseInt(selectedSupplierId));

                // Check if the selected supplier has purchase orders
                if (selectedSupplier && selectedSupplier.supplier_purchase_orders.length > 0) {
                    // Remove the default option and add new ones
                    billNoSelect.empty().append(new Option('<?= __('-- Select a Bill No --') ?>', ''));

                    selectedSupplier.supplier_purchase_orders.forEach(order => {
                        billNoSelect.append(new Option(order.bill_no, order.stock_request_id));
                        console.log(order.supplier_bill_no);
                    });

                    // selectedSupplier.supplier_purchase_orders.forEach(order => {
                    //     const option = new Option(order.bill_no, order.stock_request_id);
                    //     $(option).attr('data-supplier-bill-no', order.supplier_bill_no); // Add data attribute
                    //     billNoSelect.append(option);
                    // });
                }
            }

            // Refresh the Select2 instance for the billNoSelect dropdown
            billNoSelect.trigger('change.select2');
        // });
    });

    $(document).on("change", ".product_option", function (event) {
        if($('option:selected', this).val() !== 0)
        {
            var productId = $('option:selected', this).val();

            if(productId == 0)
            {
                $(this).closest("tr").find('#product_sku').text('');
                return false;
            }

            $('#po-attribute-dropdown').empty();
            $('#po-attribute-dropdown').append('<option value="">' + 'Select Attribute' + '</option>');
            $('#po_variant_id').empty();
            $('#po_variant_id').append('<option value="">' + 'Select Variant' + '</option>');
            
            $(this).closest("tr").find('#product_sku').text('');
            var product_sku = $('option:selected', this).attr('data-product-sku');

            if (productId !== 0) {
                
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'WarehouseStockRequests', 'action' => 'getVariants']) ?>/' + productId,
                    type: 'GET',
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function(response) {
                        
                        if (response.variants && Object.keys(response.variants).length > 0)
                        {
                            var variantDropdown = $('#po_variant_id');

                            // Clear the existing options in the variant dropdown
                            variantDropdown.empty();

                            const firstVariantKey = Object.keys(response.variants)[0];
                            const firstSKU = response.variants[firstVariantKey].sku;

                            $('#po_variant_id').closest("tr").find('#product_sku').text(firstSKU);

                            $.each(response.variants, function(id, variant) {
                                variantDropdown.append(
                                    $('<option>', {
                                        value: id,
                                        text: variant.name,
                                        'data-sku': variant.sku // Store the SKU in a data attribute
                                    })
                                );
                            });

                        }
                        else
                        {
                            $('#po_variant_id').closest("tr").find('#product_sku').text(product_sku);
                            $('#po_variant_id').empty();
                            $('#po_variant_id').append('<option value="">No variants available</option>');
                        }

                        if (response.attributes.length > 0) {

                            $('#po-attribute-dropdown').empty(); // Clear existing options

                            response.attributes.forEach(function(attribute) {
                                $('#po-attribute-dropdown').append(
                                    $('<option>', {
                                        value: attribute.attribute_id,
                                        text: attribute.attribute_name + ': ' + attribute.attribute_value
                                    })
                                );
                            });

                        } else {
                            $('#po-attribute-dropdown').empty();
                            $('#po-attribute-dropdown').append('<option value="">No attributes available</option>');
                        }
                    },
                    error: function() {

                        swal('<?= __('Failed') ?>', '<?= __('Unable to load variants. Please try again.') ?>', 'error');
                    }
                });
            }
        }
        else
        {
            $(this).closest("tr").find('#product_sku').text('');
            $(this).closest("tr").find('#product_supplier').text('');
            $(this).closest("tr").find('#product_image').attr('src','');
        }

    });

    $("table.add_product_table").on("click", ".updateQuest", function (event) {
        const selectedSupplierId = $('#supplierSelect').val();
        let $row = $(this).closest("tr");
        var $button = $(this); // Store the clicked button

        let product_id = $row.find('input[name="product_id[]"]').val();
        let product_name = $row.find('#product_name').val();
        let sku = $row.find('#product_sku').text().trim();
        let variant_name = $row.find('#variant_name').val();
        let attribute_name = $row.find('#attribute_name').val();

        let product_variant_id = $row.find('input[name="product_variant_id[]"]').val();
        let product_attribute_id = $row.find('input[name="product_attribute_id[]"]').val();
        
        let remaining_quantity = $row.find('input[name="remaining_quantity[]"]').val();

        let quantity = $row.find('input[name="product_quantity[]"]').val();
        let defective_quantity = $row.find('input[name="defective_quantity[]"]').val();
        let damaged_quantity = $row.find('input[name="damaged_quantity[]"]').val();
        let stock_movement_item_id = $row.find('#stock_movement_item_id').val();

        let bl_item_id =  $row.find('#bl_item_id').val();
        let first_product_count =  $row.find('#first_product_count').val();

        const today = new Date();

        const dd = String(today.getDate()).padStart(2, '0');
        const mm = String(today.getMonth() + 1).padStart(2, '0'); // Months are 0-based
        const yy = String(today.getFullYear()).slice(-2); // Last 2 digits of the year

        const formattedDate = `${dd}-${mm}-${yy}`;
        $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'WarehouseStockIncoming', 'action' => 'addIncomingStock']) ?>/' + selectedSupplierId,
                    method: 'POST',
                    headers: {
                            'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                        },
                         data: {
                            stock_movement_id: $('#stock_movement_id').val(),
                            stock_request_id: $('#stock_request_id').val(),
                            movement_date: formattedDate,
                            bl_item_id: bl_item_id,
                            stock_movement_item_id: stock_movement_item_id,
                            first_product_count: first_product_count,
                            // document: $('#document').val(), // use FormData for file uploads
                            bl_no   : $('#bl_no').val(),
                            product_id: Number(product_id),
                            product_variant_id: Number(product_variant_id),
                            product_attribute_id: Number(product_attribute_id),
                            remaining_quantity: Number(remaining_quantity),
                            product_quantity: Number(quantity),
                            defective_quantity:Number(defective_quantity),
                        },
                    success: function (response) {
                        console.log(response);
                        if (typeof response === 'string') {
                            response = JSON.parse(response);
                               

                        }
                        var listurl = '/warehouse-stock-incoming/itemList/'+response.stock_movement_item_id+'/'+first_product_count+'/'+quantity;
                        $row.find('.showItems').show();
                        $row.find('.showItems').attr('href', listurl).attr('target', '_self');
                        $('#stock_movement_id').val(response.stock_movement_id);
                        $row.find('#stock_movement_item_id').val(response.stock_movement_item_id);
                            
                        $button.hide();
                        let remaining_quantity = $row.find('input[name="remaining_quantity[]"]').val();

 $row.find('input[name="product_quantity[]"]').prop('disabled', true);
$row.find('input[name="defective_quantity[]"]').prop('disabled', true);
$row.find('input[name="damaged_quantity[]"]').prop('disabled', true);
                    if(response.new_remaining_quantity != 0)
{
                        var newRow = $("<tr>");
                        var cols = "";
                        cols += '<td class="col-sm-3"><input type="text" min="1" class="form-control product_id" id="product_name" value="'+product_name+'" disabled><input type="hidden" min="1" class="form-control product_id" id="product_id" value="'+product_id+'" name="product_id[]"><input type="hidden" min="1" class="form-control stock_movement_item_id" id="stock_movement_item_id" name="stock_movement_item_id" value="'+response.stock_movement_item_id+'"></td>';

                        cols += '<td><input type="hidden" min="1" class="form-control variant_id" id="variant_name" value="'+product_variant_id+'" name="product_variant_id[]"><input type="text" min="1" class="form-control variant_id" id="variant_name" value="'+variant_name+'" disabled ></td>';
                        cols += '<td><input type="hidden" min="1" class="form-control attribute_id" id="attribute_id" value="'+product_attribute_id+'" name="product_attribute_id[]"><input type="text" min="1" class="form-control attribute_id" id="" value="'+attribute_name+'" disabled ></td>';
                            cols += '<td><span id="product_sku">'+sku+'</span></td>';
                            cols += '<td><input type="number" min="1" class="form-control remaining_quantity" id="remaining_quantity" value = "'+response.new_remaining_quantity+'" name="remaining_quantity[]"><span id="remaining_quantity_error" style="color: #dc3545;"></span></td>';
                            cols += '<td><input type="number" min="1" class="form-control product_quantity" id="product_quantity" value = "" name="product_quantity[]"><span id="product_quantity_error" style="color: #dc3545;"></span></td>';
                            cols += '<td><input type="number" min="1" class="form-control defective_quantity" id="defective_quantity" name="defective_quantity[]" value=""><span id="defective_quantity_error" style="color: #dc3545;"></span></td>';
                            cols += '<td><input type="number" min="1" class="form-control damaged_quantity" id="damaged_quantity" name="damaged_quantity[]" value=""><span id="damaged_quantity_error" style="color: #dc3545;"></span></td>';
                            
                            cols += '<td><input type="hidden" name="bl_item_id" id="bl_item_id" value="'+bl_item_id+'">';

                            if('stock_movement_item_id' in response)
                        {
                            cols += '<button type="button" id="updateQuest" class="btn btn-md updateQuest">Save</button><a  id="showItems" class="btn btn-md showItems" style="display: none;">  Items</a><input type="hidden" name="first_product_count" id="first_product_count" value='+response.first_product_count+' >';
                        }    
                                    newRow.append(cols);
                                    console.log(newRow);

                                    $("table.add_product_table").append(newRow);
                    }

                        swal('<?= __('Success') ?>', '<?= __('Incoming Stock is created successfully.') ?>', 'success');


                    },
                    error: function () {
                        
                        swal('<?= __('Failed') ?>', '<?= __('Error fetching products!') ?>', 'error');
                    }
                });

    });
    $('#add_product_table').on('input', '#product_quantity', function () {
        let qty = parseInt($(this).val()) || 0;
        let remaining = parseInt($(this).closest('tr').find('#remaining_quantity').val()) || 0;
        if (qty > remaining) {
            swal('<?= __('Invalid Quantity') ?>', '<?=  __('Quantity must be less than remaining quantity.') ?>', 'error');
            $(this).val(remaining); // reset to valid value
        }
    });

     $('#add_product_table').on('input', '#defective_quantity,#damaged_quantity', function () {
        let remaining = parseInt($(this).closest('tr').find('#remaining_quantity').val()) || 0;
        let defective = parseInt($(this).closest('tr').find('#defective_quantity').val()) || 0;
        let damaged = parseInt($(this).closest('tr').find('#damaged_quantity').val()) || 0;
        let defectiveanddamaged = parseInt($(this).closest('tr').find('#defective_quantity').val()) + parseInt($(this).closest('tr').find('#damaged_quantity').val())  || 0;
        if (defectiveanddamaged > remaining) {
            swal('<?= __('Invalid Quantity') ?>', '<?=  __('Defective and damaged quantity must be less than remaining quantity.') ?>', 'error');
            if($(this).attr('id') == 'defective_quantity'){
                $(this).val(remaining - damaged); // reset to valid value
            }
            if($(this).attr('id') == 'damaged_quantity'){
                $(this).val(remaining - defective); // reset to valid value
            }
        }
    });
    $("table.add_product_table").on("click", ".ibtnDel", function (event) {    
         $(this).closest("tr").remove(); 
    });
    
    $('#po_variant_id').change(function() {
        var selectedSku = $(this).find('option:selected').data('sku');
        
        $(this).closest("tr").find('#product_sku').text(selectedSku);
    });
    
    // $('#billNoSelect').on('change', function () {

    //     var selectedBillId = $(this).val();
    //     var selectedBillNo = $(this).find('option:selected').text();
    //     var selectedSupplierBillNo = $(this).find('option:selected').attr('data-supplier-bill-no');

    //     $('#pon').val(selectedBillNo);
    //     $('#supplier-bill-no').val(selectedSupplierBillNo);

    //     // Check if a Bill is selected
    //     if (selectedBillId) {

    //         // $('#stockTable tbody').empty();
    //         // $('#add_product_table tbody tr:not(:first-child)').remove();

    //         // Make an AJAX call to fetch StockRequestItems based on selected Bill ID
    //         $.ajax({
    //             url: "<?= $this->Url->build(['controller' => 'WarehouseStockIncoming', 'action' => 'getStockRequestItems']) ?>",
    //             method: 'GET',
    //             data: { stock_request_id: selectedBillId },
    //             headers: {
    //                 'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
    //             },
    //             success: function (response) {

    //                 // If data is found, show the table and populate it
    //                 if (response.success && response.data.length) {
    //                     // $('#stockTable').show();
    //                     $('#add_product_table').show();
    //                     var tableData = '';

    //                     // Loop through the response data and generate table rows
    //                     $.each(response.data, function (index, item) {

    //                     tableData += `<tr>

    //                             <td><input type="hidden" class="form-control" name="product_id[]" value="${item.product_id}">${item.product_name}</td>

    //                             <td><input type="hidden" class="form-control" name="product_variant_id[]" value="${item.product_variant_id}">${item.product_variant}</td>

    //                             <td><input type="hidden" class="form-control" name="product_attribute_id[]" value="${item.product_attribute_id}">${item.product_attribute}</td>

    //                             <td><input type="hidden" class="form-control" name="sku[]" value="${item.sku}">${item.sku}</td>


    //                             <td>
    //                                 <input type="number" min="1" class="form-control" name="quantity[]" value="${item.requested_quantity}">
    //                             </td>

    //                             <td><button type="button" class="ibtnDel btn admin_btn"><?= __('Delete') ?></button></td>
    //                             </tr>
    //                     `;

    //                     });

    //                     // Append rows to table
    //                     // $('#table_datalist').html(tableData);
    //                     $('#add_product_table tr:last').after(tableData);
    //                 } else {
    //                     // Handle if no data is found
    //                     swal('<?= __('Failed') ?>', '<?= __('No items found for the selected Bill.') ?>', 'error');
    //                 }
    //             },
    //             error: function () {
    //                 swal('<?= __('Failed') ?>', '<?= __('Failed to fetch products. Please try again.') ?>', 'error');
    //             }
    //         });
    //     } else {
    //         // If no bill is selected, hide the table
    //         // $('#stockTable').hide();
    //         $('#add_product_table').hide();
    //     }
    // });


    $('#save').on('click', function(event) {
        event.preventDefault(); // Prevent the default form submission

        let error = 0;
        if($("#supplierSelect").val() == '' || $("#supplierSelect").val() == null || $("#supplierSelect").val() == '<?= __('Select Supplier') ?>')
        {
            error = 1;
            $('#supplier_error').show();
            $('#supplierSelect').next('.select2-container').find('.select2-selection').addClass('is-invalid');

        }

        if($("#billNoSelect option:selected").val() == '' || $("#billNoSelect option:selected").val() == null || $("#billNoSelect option:selected").val() == '<?= __('Select Bill No') ?>')
        {
            error = 1;
            $('#bill_no_error').show();
            $('#billNoSelect').next('.select2-container').find('.select2-selection').addClass('is-invalid');

        }

        if($("#movement_date").val() == '' || $("#movement_date").val() == null)
        {
            error = 1;
            $('#movement_date_error').show();
            $('#movement_date').addClass('is-invalid');
        }

        if($("#supplier-bill-no").val() == '' || $("#supplier-bill-no").val() == null)
        {
            error = 1;
            $('#supplier_bill_no_error').show();
            $('#supplier-bill-no').addClass('is-invalid');
        }
        
        // Check if a document is uploaded
        let fileInput = $("#imageInput")[0].files;
        if (fileInput.length === 0) {
            error = 1;
            $('#document_error').show();
            $('#imageInput').addClass('is-invalid');
        }

        if(error == 0)
        {
            $('#supplier_error').hide();
            $('#bill_no_error').hide();
            $('#movement_date_error').hide();
            $('#supplier_bill_no_error').hide();
            $('#document_error').hide();
            $('#save').attr('disabled','disabled');
            $('#add_stock_incoming_form').submit();
        }
    });

</script>

<?php $this->end(); ?>